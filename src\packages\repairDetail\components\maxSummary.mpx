<template>
  <t-popup
    visible="{{visible}}"
    bind:visible-change="onVisibleChange"
    placement="bottom"
  >
    <view class="max-summary">
      <view class="block">
        <view class="header">
          <view class="title">已为您总结留言内容</view>
          <view bindtap="onVisibleChange" class="close"
            ><image src="{{TechMaintainUrl.ClosePopup}}" class="close-icon"
          /></view>
        </view>
      </view>
      <scroll-view
        class="summary"
        scroll-y="true"
        scroll-with-animation
        scroll-into-view="{{lastSummaryId}}"
      >
        <block wx:if="{{loadingTowxml}}">
          <view class="loading-text">加载富文本功能中...</view>
        </block>
        <block wx:elif="{{comment && comment.children && comment.children.length > 0}}">
          <towxml nodes="{{comment}}" />
        </block>
        <block wx:else>
          <view class="fallback-text">{{fallbackText}}</view>
          <view class="debug-info">
            <text>Debug: towxmlLoaded={{towxmlLoaded}}</text>
            <text>comment存在: {{comment ? '是' : '否'}}</text>
            <text>comment.children存在: {{comment.children ? '是' : '否'}}</text>
            <text>comment.children长度: {{comment.children ? comment.children.length : 0}}</text>
          </view>
        </block>
        <view id="bottomAnchor"></view>
      </scroll-view>
    </view>
  </t-popup>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { addGlobalEventListener } from 'shared/utils/emit';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { IsAllowOEMVisible } from 'shared/utils/constant';
  import * as towxml from '../vendor/towxml/index';
  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false
      }
    },
    computed: {},
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      TechMaintainUrl,
      comment: {},
      lastSummaryId: '',
      loadingTowxml: false,
      towxmlLoaded: false,
      fallbackText: ''
    },
    lifetimes: {
      created() {
        addGlobalEventListener('getRepairOrderNumber', orderNumber => {
          console.log('能够拿到orderNumber', orderNumber);
          this.orderNumber = orderNumber;
        });
      },
      attached() {
        console.log('检查towxml引入了没有', towxml);
        this.setData({
          lastSummaryId: 'bottomAnchor'
        });
        if (typeof towxml === 'function') {
          this.setData({ towxmlLoaded: true });
        } else {
          this.setData({ towxmlLoaded: false });
        }
      },
      detached() {
        console.log('组件卸载，清除轮询');
        this.clearPolling();
      },
      ready() {}
    },
    watch: {
      visible(newVal) {
        if (newVal) {
          console.log('组件显示，重置数据');
          this.resetData();
          this.setData({ loadingTowxml: true });
          this.getCommentSummaryRequest()
            .then(() => {
              if (!this.traceId || !this.reqId) {
                wx.showToast({
                  title: '获取留言总结失败，请稍后重试',
                  icon: 'none'
                });
                return;
              }
              this.pollingCommentSummary();
            })
            .finally(() => {
              this.setData({ loadingTowxml: false });
            });
        } else {
          console.log('组件隐藏，清除轮询');
          this.resetData();
          this.clearPolling();
          console.log('此时的intervalId:', this.intervalId);
        }
      }
    },
    methods: {
      onVisibleChange() {
        console.log('关闭弹窗');
        this.triggerEvent('close');
      },
      resetData() {
        this.setData({
          comment: {},
          scrollTop: 0,
          lastSummaryId: '',
          fallbackText: ''
        });
        this.traceId = null;
        this.reqId = null;
        this.pollingActive = false;
      },
      clearPolling() {
        if (this.intervalId) {
          console.log('清除轮询');
          clearInterval(this.intervalId);
          this.intervalId = null;
          this.pollingActive = false;
        }
      },
      async getCommentSummaryRequest() {
        console.log('有维修单号:', this.orderNumber);
        try {
          const res = await this.techMaintenanceApi.commentSummaryRequest(
            this.orderNumber
          );
          if (res.code === HTTPSTATUSCODE.Success && res.data) {
            this.traceId = res.data.traceId;
            this.reqId = res.data.reqId;
          } else {
            wx.showToast({
              title: res.message || '获取留言总结失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('Error in getCommentSummary:', error);
          wx.showToast({
            title: '获取留言总结失败',
            icon: 'none'
          });
        }
      },
      async getCommentSummaryResponse() {
        console.log('获取留言总结响应');
        try {
          const res = await this.techMaintenanceApi.commentSummaryResponse({
            number: this.orderNumber,
            traceId: this.traceId,
            reqId: this.reqId
          });
          if (res.code === HTTPSTATUSCODE.Success && res.data) {
            let content: any = {};
            let fallbackText = res.data.responseAll || '';

            if (towxml && typeof towxml === 'function') {
              try {
                content = towxml(res.data.responseAll, 'markdown');
                console.log('towxml解析成功，获取到的留言总结内容:', content);
              } catch (error) {
                console.error('towxml解析失败:', error);
                content = {};
              }
            } else {
              console.log('towxml未正确加载');
              content = {};
            }

            this.setData({
              comment: content,
              fallbackText: fallbackText
            });

            if (res.data.finished === true) {
              this.clearPolling();
            }
          } else {
            wx.showToast({
              title: res.message || '获取留言总结失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('Error in getCommentSummaryResponse:', error);
          wx.showToast({
            title: '获取留言总结失败',
            icon: 'none'
          });
        }
      },
      pollingCommentSummary() {
        this.pollingActive = true;
        this.intervalId = setInterval(() => {
          if (!this.pollingActive) {
            this.clearPolling();
            return;
          }
          this.getCommentSummaryResponse();
        }, 500);
      }
    }
  });
</script>

<style lang="scss" scoped>
  .max-summary {
    height: 1000rpx;

    .block {
      width: 100vw;
      border: #fff 1px solid;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      background: linear-gradient(
        90deg,
        #e3e9fc 100%,
        #efecfb 100%,
        #f4e9f6 100%
      );
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 80rpx;
        padding: 0 24rpx;
      }
      .title {
        text-align: center;
        font-weight: 600;
        font-size: 32rpx;
      }
      .close {
        width: 32rpx;
        height: 32rpx;
        .close-icon {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
    .summary {
      width: 100%;
      height: calc(100% - 80rpx);
      padding: 16rpx 32rpx;
      box-sizing: border-box;
      overflow-y: auto;

      view {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
      }

      .loading-text {
        text-align: center;
        color: #999;
        padding: 40rpx 0;
      }

      .fallback-text {
        white-space: pre-wrap;
        word-break: break-word;
      }

      .debug-info {
        margin-top: 20rpx;
        padding: 20rpx;
        background-color: #f5f5f5;
        border-radius: 8rpx;

        text {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin-bottom: 8rpx;
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-popup": "tdesign-miniprogram/popup/popup"
    }
  }
</script>
