<template>
  <view>测试 towxml 组件</view>
  <block wx:if="{{test.children}}">
    <towxml nodes="{{test}}"/>
  </block>
  <block wx:else>
    <view>Fallback: {{testText}}</view>
  </block>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import * as towxml from '../vendor/towxml/index';

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      test: {},
      testText: ''
    },
    methods: {},
    lifetimes: {
      created() {},
      attached() {
        const testMarkdown = '# 测试标题\n\n这是一个**粗体**文本和*斜体*文本的测试。\n\n- 列表项1\n- 列表项2\n- 列表项3';
        let result = {};
        let fallbackText = testMarkdown;

        try {
          result = towxml(testMarkdown, 'markdown');
          console.log('另一个组件打印towxml解析结果:', result);
        } catch (error) {
          console.error('towxml解析失败:', error);
          result = {};
        }

        this.setData({
          test: result,
          testText: fallbackText
        });
      },
      detached() {},
      ready() {}
    }
  });
</script>

<style lang="scss">
  .maintain-records {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "message-card": "./messageCard.mpx",
      "towxml": "../vendor/towxml/towxml"
    }
  }
</script>