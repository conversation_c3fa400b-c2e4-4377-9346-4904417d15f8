<template></template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      test: {}
    },
    methods: {},
    lifetimes: {
      created() {},
      attached() {},
      detached() {},
      ready() {}
    }
  });
</script>

<style lang="scss">
  .maintain-records {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "message-card": "./messageCard.mpx",
      
    }
  }
</script>