<template>
  <view class="towxml-test">
    <view class="title">Towxml 组件测试页面</view>
    
    <view class="test-section">
      <text class="section-title">1. 基础组件测试</text>
      <view class="test-item">
        <text>直接使用 towxml 组件:</text>
        <towxml nodes="{{simpleNodes}}" />
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">2. 组件存在性检查</text>
      <view class="test-item">
        <text>组件检查结果: {{componentCheckResult}}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">3. 错误信息</text>
      <view class="test-item">
        <text>{{errorMessage}}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import { createComponent } from '@mpxjs/core';

createComponent({
  data: {
    simpleNodes: {
      theme: 'light',
      children: [
        {
          tag: 'view',
          class: 'h2w__p',
          style: 'color: red; font-size: 32rpx;',
          children: [
            {
              text: '如果你能看到这段红色文字，说明 towxml 组件工作正常！'
            }
          ]
        }
      ]
    },
    componentCheckResult: '检查中...',
    errorMessage: ''
  },
  lifetimes: {
    attached() {
      this.checkTowxmlComponent();
    }
  },
  methods: {
    checkTowxmlComponent() {
      try {
        // 检查组件是否存在
        const query = this.createSelectorQuery();
        query.selectAll('towxml').boundingClientRect((res) => {
          console.log('towxml 组件查询结果:', res);
          if (res && res.length > 0) {
            this.setData({
              componentCheckResult: `找到 ${res.length} 个 towxml 组件`
            });
          } else {
            this.setData({
              componentCheckResult: '未找到 towxml 组件',
              errorMessage: '组件可能未正确注册或路径有误'
            });
          }
        }).exec();

        // 延迟检查，给组件渲染时间
        setTimeout(() => {
          const query2 = this.createSelectorQuery();
          query2.selectAll('towxml').boundingClientRect((res) => {
            console.log('延迟检查 towxml 组件结果:', res);
            if (!res || res.length === 0) {
              this.setData({
                errorMessage: '组件渲染失败，可能是路径或依赖问题'
              });
            }
          }).exec();
        }, 1000);

      } catch (error) {
        console.error('检查 towxml 组件时出错:', error);
        this.setData({
          componentCheckResult: '检查失败',
          errorMessage: error.message || '未知错误'
        });
      }
    }
  }
});
</script>

<style lang="scss">
.towxml-test {
  padding: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 40rpx;
    color: #333;
  }

  .test-section {
    margin-bottom: 40rpx;
    border: 2rpx solid #ddd;
    border-radius: 8rpx;
    padding: 20rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #1890ff;
      display: block;
      margin-bottom: 20rpx;
    }

    .test-item {
      margin-bottom: 20rpx;
      
      text {
        font-size: 24rpx;
        color: #666;
        display: block;
        margin-bottom: 10rpx;
      }
    }
  }
}
</style>

<script type="application/json">
{
  "component": true,
  "usingComponents": {}
}
</script>
