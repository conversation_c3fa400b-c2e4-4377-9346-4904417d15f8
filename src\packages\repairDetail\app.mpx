
<script>
  import { createApp } from '@mpxjs/core';
  createApp({
    onLaunch() {
      console.log('repairDetail 分包初始化');
    }
  });
</script>
<script type="application/json">
  {
    "pages": [
      {
        "path": "pages/repairDetail/index",
        "src": "./pages/repairDetail/index"
      }
    ],
    "usingComponents": {
      "towxml": "/packages/repairDetail/vendor/towxml/towxml",
      "decode": "/packages/repairDetail/vendor/towxml/decode",
      "table": "/packages/repairDetail/vendor/towxml/table/table",
      "todogroup": "/packages/repairDetail/vendor/towxml/todogroup/todogroup",
      "img": "/packages/repairDetail/vendor/towxml/img/img"
    }
  }
</script>
