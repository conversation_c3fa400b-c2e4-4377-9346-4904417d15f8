
<script>
  import { createApp } from '@mpxjs/core';
  createApp({
    onLaunch() {
      console.log('repairDetail 分包初始化');
    }
  });
</script>
<script type="application/json">
  {
    "pages": [
      {
        "path": "pages/repairDetail/index",
        "src": "./pages/repairDetail/index"
      }
    ],
    "usingComponents": {
      "towxml": "/repairDetail/vendor/towxml/towxml",
      "decode": "/repairDetail/vendor/towxml/decode",
      "table": "/repairDetail/vendor/towxml/table/table",
      "todogroup": "/repairDetail/vendor/towxml/todogroup/todogroup",
      "img": "/repairDetail/vendor/towxml/img/img"
    }
  }
</script>
